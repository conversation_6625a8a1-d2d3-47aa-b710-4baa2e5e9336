# Hugging Face Spaces deployment for LMArena Bridge
import os
import gradio as gr
import threading
import time
from local_openai_history_server import app

def create_interface():
    """创建Gradio界面"""
    
    def get_api_info():
        space_name = os.environ.get("SPACE_ID", "your-space-name")
        if space_name != "your-space-name":
            api_url = f"https://{space_name}.hf.space/v1"
            health_url = f"https://{space_name}.hf.space/health"
        else:
            api_url = "https://your-username-lmarena-bridge.hf.space/v1"
            health_url = "https://your-username-lmarena-bridge.hf.space/health"
        
        return f"""
# 🚀 LMArena Bridge API 服务

这是一个将LMArena.ai桥接为OpenAI API格式的服务，让你可以在任何支持OpenAI API的客户端中使用LMArena上的模型。

## 📡 API 信息
- **API Base URL**: `{api_url}`
- **健康检查**: `{health_url}`
- **模型列表**: `{api_url}/models`
- **聊天接口**: `{api_url}/chat/completions`

## 🔧 使用步骤

### 第一步：安装油猴脚本
1. 在浏览器中安装 [Tampermonkey](https://www.tampermonkey.net/) 扩展
2. 下载并安装配套的油猴脚本（见下方下载链接）
3. **重要**：修改脚本中的服务器地址为上面的API Base URL

### 第二步：打开LMArena
1. 访问 [LMArena.ai](https://lmarena.ai/)
2. 进入任意一个DirectChat的**历史对话**页面（不要是新对话）
3. 刷新页面，确保油猴脚本正常加载

### 第三步：配置客户端
在任何支持OpenAI API的应用中配置：
- **API地址**: `{api_url}`
- **API密钥**: 任意值（如：`sk-test`）

## 📱 客户端配置示例

### Python代码
```python
import openai

client = openai.OpenAI(
    api_key="sk-any-key",  # 任意值
    base_url="{api_url}"
)

response = client.chat.completions.create(
    model="claude-3-5-sonnet-20241022",
    messages=[{{"role": "user", "content": "Hello!"}}]
)
print(response.choices[0].message.content)
```

### 手机APP配置
- **ChatGPT Next Web**: 设置API地址为上面的URL
- **OpenAI Translator**: 同样设置API地址
- **其他OpenAI兼容应用**: 都可以使用

## ⚠️ 重要提醒
- 此API需要配合浏览器中的油猴脚本使用
- 使用时必须保持LMArena页面在浏览器中打开
- 首次使用前请确保油猴脚本已正确配置服务器地址
        """
    
    with gr.Blocks(title="LMArena Bridge API", theme=gr.themes.Soft()) as interface:
        gr.Markdown(get_api_info())
        
        with gr.Row():
            test_btn = gr.Button("🔍 测试API健康状态", variant="primary")
            status_output = gr.Textbox(label="测试结果", interactive=False, lines=3)
        
        def test_api():
            try:
                import requests
                health_url = "http://localhost:7860/health"
                response = requests.get(health_url, timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    return f"✅ API服务正常运行\n服务: {data.get('service', 'Unknown')}\n版本: {data.get('version', 'Unknown')}"
                else:
                    return f"⚠️ API响应异常，状态码: {response.status_code}"
            except Exception as e:
                return f"❌ API连接失败: {str(e)}"
        
        test_btn.click(test_api, outputs=status_output)
        
        gr.Markdown("""
## 📥 下载文件
- [油猴脚本下载](./file=TampermonkeyScript/LMArenaAutomator_Cloud.js)
- [完整使用指南](./file=DEPLOYMENT_GUIDE.md)
        """)
    
    return interface

def run_flask_server():
    """在后台运行Flask服务器"""
    app.run(host="0.0.0.0", port=7860, debug=False)

if __name__ == "__main__":
    # 在后台启动Flask服务器
    flask_thread = threading.Thread(target=run_flask_server, daemon=True)
    flask_thread.start()
    
    # 等待Flask服务器启动
    time.sleep(3)
    
    print("🚀 LMArena Bridge API 服务已启动")
    print("📡 API地址: http://localhost:7860/v1")
    print("🌐 Web界面: http://localhost:7860")
    
    # 启动Gradio界面
    interface = create_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )
