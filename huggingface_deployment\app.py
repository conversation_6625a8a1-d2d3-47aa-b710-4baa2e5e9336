# Hugging Face Spaces deployment for LMArena Bridge
import os
import gradio as gr
import secrets
import threading
import time
from local_openai_history_server import app

# 生成随机API密钥
API_SECRET = os.environ.get("API_SECRET", secrets.token_urlsafe(32))
print(f"🔐 API密钥: {API_SECRET}")
print("⚠️ 请保存此密钥，这是你的专用API访问密钥！")

# 设置Flask应用的API密钥
os.environ["API_SECRET"] = API_SECRET

def create_interface():
    """创建安全的Gradio界面"""

    def get_api_info():
        # 尝试获取Space信息
        space_id = os.environ.get("SPACE_ID", "")
        space_author = os.environ.get("SPACE_AUTHOR_NAME", "")

        if space_id and space_author:
            api_url = f"https://{space_author}-{space_id}.hf.space/v1"
            health_url = f"https://{space_author}-{space_id}.hf.space/health"
        else:
            api_url = "https://your-username-lmarena-bridge.hf.space/v1"
            health_url = "https://your-username-lmarena-bridge.hf.space/health"

        return f"""
# 🔐 LMArena Bridge API 服务（私人专用）

## 📡 服务状态
- **API Base URL**: `{api_url}`
- **健康检查**: `{health_url}`
- **安全状态**: 🔒 API密钥保护已启用

## 🔑 API密钥
**你的专用API密钥**: `{API_SECRET}`

⚠️ **重要安全提醒**：
- 请妥善保管此密钥，不要分享给他人
- 此密钥用于所有API请求的身份验证
- 如需重新生成密钥，请重启Space

## 📱 客户端配置
```python
import openai

client = openai.OpenAI(
    api_key="{API_SECRET}",  # 使用上面的密钥
    base_url="{api_url}"
)
```

## 🐵 油猴脚本配置
1. 修改脚本中的服务器地址为: `{api_url.replace('/v1', '')}`
2. 在LMArena.ai打开历史对话页面
3. 开始使用API

## 🔒 隐私保护
- 此服务仅供你个人使用
- 所有API请求都需要密钥验证
- 建议将Space设置为Private
        """
    
    with gr.Blocks(title="LMArena Bridge API", theme=gr.themes.Soft()) as interface:
        gr.Markdown(get_api_info())
        
        with gr.Row():
            test_btn = gr.Button("🔍 测试API健康状态", variant="primary")
            status_output = gr.Textbox(label="测试结果", interactive=False, lines=3)
        
        def test_api():
            try:
                import requests
                health_url = "http://localhost:5102/health"
                response = requests.get(health_url, timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    return f"✅ API服务正常运行\n服务: {data.get('service', 'Unknown')}\n版本: {data.get('version', 'Unknown')}\n安全状态: {data.get('security', 'Unknown')}\n密钥提示: {data.get('api_key_hint', 'Unknown')}"
                else:
                    return f"⚠️ API响应异常，状态码: {response.status_code}"
            except Exception as e:
                return f"❌ API连接失败: {str(e)}\n请等待Flask服务器启动..."
        
        test_btn.click(test_api, outputs=status_output)
        
        gr.Markdown("""
## 📥 下载文件
- [油猴脚本下载](./file=TampermonkeyScript/LMArenaAutomator_Cloud.js)
- [完整使用指南](./file=DEPLOYMENT_GUIDE.md)
        """)
    
    return interface

# 在后台运行Flask服务器
def run_flask_in_background():
    """在后台运行Flask API服务器"""
    try:
        print("🚀 启动Flask API服务器...")
        # 使用不同的端口运行Flask，然后通过代理转发
        app.run(host="127.0.0.1", port=5102, debug=False, threaded=True)
    except Exception as e:
        print(f"❌ Flask服务器启动失败: {e}")

# 启动Flask后台服务
flask_thread = threading.Thread(target=run_flask_in_background, daemon=True)
flask_thread.start()
time.sleep(2)  # 等待Flask启动

# 创建Gradio界面
interface = create_interface()

# 添加API代理路由
def setup_api_proxy(app):
    """设置API代理，将请求转发到Flask服务器"""
    import requests
    from fastapi import Request, HTTPException
    from fastapi.responses import Response, StreamingResponse

    @app.get("/health")
    @app.get("/")
    async def proxy_health():
        try:
            response = requests.get("http://localhost:5102/health", timeout=10)
            return response.json()
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    @app.get("/v1/models")
    async def proxy_models(request: Request):
        try:
            headers = dict(request.headers)
            response = requests.get("http://localhost:5102/v1/models", headers=headers, timeout=10)
            return response.json()
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    @app.post("/v1/chat/completions")
    async def proxy_chat(request: Request):
        try:
            headers = dict(request.headers)
            body = await request.body()
            response = requests.post(
                "http://localhost:5102/v1/chat/completions",
                headers=headers,
                data=body,
                timeout=30,
                stream=True
            )

            def generate():
                for chunk in response.iter_content(chunk_size=1024):
                    if chunk:
                        yield chunk

            return StreamingResponse(
                generate(),
                media_type=response.headers.get('content-type', 'application/json')
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    # 代理其他API端点
    for endpoint in ["/get_injection_job", "/injection_complete", "/submit_prompt_job",
                     "/get_prompt_job", "/prompt_complete", "/get_config", "/report_models"]:

        @app.get(endpoint)
        @app.post(endpoint)
        async def proxy_endpoint(request: Request, endpoint=endpoint):
            try:
                headers = dict(request.headers)
                if request.method == "GET":
                    response = requests.get(f"http://localhost:5102{endpoint}", headers=headers, timeout=10)
                else:
                    body = await request.body()
                    response = requests.post(f"http://localhost:5102{endpoint}", headers=headers, data=body, timeout=10)
                return response.json()
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    print("🚀 启动LMArena Bridge API服务")

    # 设置API代理
    setup_api_proxy(interface.app)

    # 启动Gradio界面（包含API代理）
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )
