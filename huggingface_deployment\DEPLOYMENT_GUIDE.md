# 🔐 LMArena Bridge - 安全版部署指南

## ⚠️ 重要安全更新

**已修复的问题**：
1. ✅ 端口配置问题 - 现在正确使用7860端口
2. ✅ 添加API密钥保护 - 防止未授权访问
3. ✅ 隐藏敏感信息 - 不再在公开页面显示详细教程

## 📁 文件说明

这个文件夹包含了部署到 Hugging Face Spaces 所需的所有文件，**不会影响你的原始项目**。

### 文件列表：
- `app.py` - Hugging Face Spaces 主入口文件
- `local_openai_history_server.py` - 修改后的服务器（云端版本）
- `requirements.txt` - Python 依赖包
- `models.json` - 模型配置文件
- `config.jsonc` - 功能配置文件
- `TampermonkeyScript/LMArenaAutomator_Cloud.js` - 云端版油猴脚本
- `DEPLOYMENT_GUIDE.md` - 本指南文件

## 🚀 部署步骤

### 第一步：创建 Hugging Face Space
1. 访问 [Hugging Face Spaces](https://huggingface.co/spaces)
2. 点击 "Create new Space"
3. 填写信息：
   - **Space name**: `lmarena-bridge` (或你喜欢的名字)
   - **SDK**: 选择 `Gradio`
   - **Hardware**: `CPU basic` (免费)
   - **Visibility**: `Public` 或 `Private`

### 第二步：上传文件
将本文件夹中的所有文件上传到你的 Space：
1. 点击 "Files" 标签
2. 点击 "Add file" → "Upload files"
3. 选择并上传以下文件：
   - `app.py`
   - `local_openai_history_server.py`
   - `requirements.txt`
   - `models.json`
   - `config.jsonc`
4. 创建 `TampermonkeyScript` 文件夹并上传：
   - `TampermonkeyScript/LMArenaAutomator_Cloud.js`

### 第三步：等待部署
- Hugging Face 会自动构建和部署
- 通常需要 2-5 分钟
- 部署完成后显示绿色 "Running" 状态

### 第四步：获取API密钥和地址
部署成功后：
1. 打开你的Space页面
2. **重要**：在Gradio界面中找到你的专用API密钥
3. 复制并妥善保存这个密钥
4. 你的API地址是：`https://你的用户名-space名称.hf.space/v1`

### 第五步：设置为Private（强烈推荐）
1. 在Space设置中将Visibility改为"Private"
2. 这样只有你能访问这个服务

## 🐵 配置油猴脚本

### 安装步骤
1. 安装 [Tampermonkey](https://www.tampermonkey.net/) 浏览器扩展
2. 打开 Tampermonkey 管理面板
3. 点击 "添加新脚本"
4. 复制 `TampermonkeyScript/LMArenaAutomator_Cloud.js` 的内容
5. **重要**：修改脚本中的两个配置：
   ```javascript
   // 1. 服务器地址
   const SERVER_URL = 'https://你的用户名-space名称.hf.space';

   // 2. API密钥（从Gradio界面复制）
   const API_SECRET = '你的专用API密钥';
   ```
6. 保存脚本

### 使用步骤
1. 打开 [LMArena.ai](https://lmarena.ai/)
2. 进入任意一个 DirectChat 的**历史对话**页面
3. 刷新页面，右上角应显示连接状态

## 📱 客户端配置

### Python 示例
```python
import openai

client = openai.OpenAI(
    api_key="你的专用API密钥",  # 使用从Gradio界面获取的密钥
    base_url="https://你的用户名-space名称.hf.space/v1"
)

response = client.chat.completions.create(
    model="claude-3-5-sonnet-20241022",
    messages=[{"role": "user", "content": "Hello!"}]
)
print(response.choices[0].message.content)
```

### 手机 APP 配置
- **API 地址**: `https://你的用户名-space名称.hf.space/v1`
- **API 密钥**: 你的专用API密钥（从Gradio界面复制）

### 测试命令
```bash
# 测试健康状态（无需密钥）
curl https://你的用户名-space名称.hf.space/health

# 获取模型列表（需要密钥）
curl -H "Authorization: Bearer 你的专用API密钥" \
     https://你的用户名-space名称.hf.space/v1/models
```

## ⚠️ 重要提醒

1. **原项目不受影响**：你的电脑版本可以继续正常使用
2. **API密钥保护**：所有API请求都需要正确的密钥
3. **设置为Private**：强烈建议将Space设置为Private
4. **浏览器必须开启**：API 需要配合浏览器中的油猴脚本
5. **保持页面打开**：使用时需要在浏览器中保持 LMArena 页面开启
6. **妥善保管密钥**：不要分享你的API密钥给他人

## 🔧 故障排除

### API 无响应
- 检查 Hugging Face Space 是否正常运行
- 确认 API 地址是否正确
- 查看 Space 的 Logs 标签

### 油猴脚本无法连接
- 确认脚本中的 SERVER_URL 是否正确
- 检查浏览器控制台错误信息
- 确保 LMArena 页面已正确加载

### 模型列表为空
- 确保 `models.json` 文件已正确上传
- 检查文件格式是否正确

## 🎉 完成！

现在你可以在任何设备上通过 API 使用 LMArena 的模型了！
