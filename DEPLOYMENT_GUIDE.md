# 🚀 LMArena Bridge - Hugging Face Spaces 完整部署指南

## 📋 部署步骤

### 第一步：准备Hugging Face账户
1. 访问 [Hugging Face](https://huggingface.co/) 并注册/登录账户
2. 进入 [Spaces](https://huggingface.co/spaces) 页面

### 第二步：创建新的Space
1. 点击 "Create new Space"
2. 填写以下信息：
   - **Space name**: `lmarena-bridge` (或你喜欢的名字)
   - **License**: `mit`
   - **SDK**: 选择 `Gradio`
   - **Hardware**: `CPU basic` (免费)
   - **Visibility**: `Public` 或 `Private`

### 第三步：上传文件
将以下文件上传到你的Space：

**必需文件：**
- `app.py` (主入口文件)
- `local_openai_history_server.py` (修改后的服务器)
- `requirements.txt` (依赖列表)
- `models.json` (模型配置)
- `config.jsonc` (功能配置)

**可选文件：**
- `README_DEPLOYMENT.md` (使用说明)
- `TampermonkeyScript/LMArenaAutomator_Cloud.js` (云端版油猴脚本)

### 第四步：等待部署
- Hugging Face会自动构建和部署你的应用
- 通常需要2-5分钟
- 部署完成后会显示绿色的"Running"状态

### 第五步：获取API地址
部署成功后，你的API地址将是：
```
https://your-username-lmarena-bridge.hf.space/v1
```

## 🔧 配置油猴脚本

### 安装步骤
1. 在浏览器中安装 [Tampermonkey](https://www.tampermonkey.net/) 扩展
2. 打开Tampermonkey管理面板
3. 点击"添加新脚本"
4. 复制 `TampermonkeyScript/LMArenaAutomator_Cloud.js` 的内容
5. **重要**：修改脚本中的服务器地址：
   ```javascript
   const SERVER_URL = 'https://your-username-lmarena-bridge.hf.space';
   ```
6. 保存脚本

### 使用步骤
1. 打开 [LMArena.ai](https://lmarena.ai/)
2. 进入任意一个DirectChat的历史对话页面
3. 刷新页面，确保油猴脚本加载
4. 右上角应该显示连接状态

## 📱 客户端配置示例

### Python OpenAI库
```python
import openai

client = openai.OpenAI(
    api_key="sk-any-key",  # 任意值
    base_url="https://your-username-lmarena-bridge.hf.space/v1"
)

response = client.chat.completions.create(
    model="claude-3-5-sonnet-20241022",
    messages=[{"role": "user", "content": "Hello!"}]
)
print(response.choices[0].message.content)
```

### cURL测试
```bash
# 测试健康状态
curl https://your-username-lmarena-bridge.hf.space/health

# 获取模型列表
curl -H "Authorization: Bearer sk-test" \
     https://your-username-lmarena-bridge.hf.space/v1/models

# 发送聊天请求
curl -X POST https://your-username-lmarena-bridge.hf.space/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-test" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }'
```

## ⚠️ 重要注意事项

1. **浏览器必须开启**：API需要配合浏览器中的油猴脚本使用
2. **LMArena页面必须打开**：需要在浏览器中保持LMArena页面开启
3. **网络稳定性**：确保浏览器和Hugging Face Spaces之间网络连接稳定
4. **免费限制**：Hugging Face免费版有使用限制，高频使用可能需要付费

## 🔍 故障排除

### API无响应
- 检查Hugging Face Space是否正常运行
- 确认API地址是否正确
- 查看Space的日志

### 油猴脚本无法连接
- 确认脚本中的SERVER_URL是否正确
- 检查浏览器控制台是否有错误信息
- 确保LMArena页面已正确加载

### 模型列表为空
- 确保`models.json`文件已正确上传
- 检查文件格式是否正确

## 📞 获取帮助

如果遇到问题，可以：
1. 查看Hugging Face Space的日志
2. 检查浏览器控制台的错误信息
3. 确认所有配置文件都已正确设置
