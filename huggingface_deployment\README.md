# 🚀 LMArena Bridge API

将 LMArena.ai 桥接为 OpenAI API 格式的云端服务。

## 🌟 特性

- 🔗 完全兼容 OpenAI API 格式
- 🌊 支持流式响应
- 🤖 支持 LMArena 上的所有模型
- 📱 可在任何设备上使用

## 🚀 快速开始

### 1. 安装油猴脚本
下载并安装 `TampermonkeyScript/LMArenaAutomator_Cloud.js`

### 2. 配置服务器地址
修改脚本中的 `SERVER_URL` 为你的 Space 地址

### 3. 打开 LMArena
在浏览器中打开 LMArena.ai 的历史对话页面

### 4. 使用 API
```python
import openai

client = openai.OpenAI(
    api_key="sk-any-key",
    base_url="https://your-space-name.hf.space/v1"
)

response = client.chat.completions.create(
    model="claude-3-5-sonnet-20241022",
    messages=[{"role": "user", "content": "Hello!"}]
)
```

## 📚 文档

详细使用说明请查看 `DEPLOYMENT_GUIDE.md`

## ⚠️ 注意事项

- 需要配合浏览器中的油猴脚本使用
- 使用时必须保持 LMArena 页面在浏览器中打开
- 首次使用前请确保油猴脚本已正确配置

## 🔗 相关链接

- [LMArena.ai](https://lmarena.ai/)
- [Tampermonkey](https://www.tampermonkey.net/)
- [原项目 GitHub](https://github.com/your-username/LMArenaBridge)
