// ==UserScript==
// @name         LMArena Automator (Cloud Version)
// @namespace    http://tampermonkey.net/
// @version      2.1
// @description  Cloud-compatible version for Hugging Face Spaces deployment
// <AUTHOR>
// @match        https://lmarena.ai/c/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // ⚠️ 重要：修改这里的服务器地址为你的Hugging Face Spaces地址
    // 例如：'https://your-username-lmarena-bridge.hf.space'
    const SERVER_URL = 'https://your-space-name.hf.space';
    
    // 如果你想使用本地服务器，请改为：
    // const SERVER_URL = 'http://127.0.0.1:5102';

    let config = {
        bypass_enabled: false,
        tavern_mode_enabled: false,
        log_server_requests: false,
        log_tampermonkey_debug: false
    };

    // --- 配置加载逻辑 ---
    async function loadConfig() {
        try {
            const response = await fetch(`${SERVER_URL}/get_config`);
            if (response.ok) {
                config = await response.json();
                console.log("LMArena Automator: Config loaded successfully from server.", config);
            } else {
                 console.warn(`LMArena Automator: Failed to fetch config from server (status: ${response.status}). Using default values.`);
            }
        } catch (e) {
            console.error("LMArena Automator: Error fetching config from server. Using default values.", e);
        }
    }

    const STORAGE_KEY = 'pending_history_injection';
    const MODEL_ID_STORAGE_KEY = 'lmarena_target_model_id';
    let isReloading = false;

    console.log(`LMArena History Forger: Script started with server: ${SERVER_URL}`);

    // 显示连接状态
    function showConnectionStatus() {
        const statusDiv = document.createElement('div');
        statusDiv.id = 'lmarena-connection-status';
        statusDiv.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: #4CAF50;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10000;
            font-family: monospace;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        `;
        statusDiv.textContent = `🔗 Connected to: ${SERVER_URL.replace('https://', '')}`;
        document.body.appendChild(statusDiv);
        
        // 3秒后淡出
        setTimeout(() => {
            statusDiv.style.opacity = '0.3';
        }, 3000);
    }

    // 页面加载完成后显示连接状态
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', showConnectionStatus);
    } else {
        showConnectionStatus();
    }

    // --- 轮询逻辑 ---
    async function pollForJob() {
        if (isReloading) return;

        console.log("LMArena History Forger: Polling for new injection job...");
        try {
            const response = await fetch(`${SERVER_URL}/get_injection_job`);
            if (response.ok) {
                const job = await response.json();
                if (job.status === 'job_available') {
                    console.log("LMArena History Forger: New injection job received:", job);
                    await handleInjectionJob(job);
                }
            }
        } catch (error) {
            console.error("LMArena History Forger: Error polling for job:", error);
            // 显示连接错误状态
            updateConnectionStatus('error');
        }

        if (!isReloading) {
            setTimeout(pollForJob, 2000);
        }
    }

    // 更新连接状态显示
    function updateConnectionStatus(status) {
        const statusDiv = document.getElementById('lmarena-connection-status');
        if (statusDiv) {
            if (status === 'error') {
                statusDiv.style.background = '#f44336';
                statusDiv.textContent = `❌ Connection Error: ${SERVER_URL.replace('https://', '')}`;
            } else if (status === 'success') {
                statusDiv.style.background = '#4CAF50';
                statusDiv.textContent = `✅ Connected: ${SERVER_URL.replace('https://', '')}`;
            }
        }
    }

    // 处理注入任务的函数（简化版，实际使用时需要完整实现）
    async function handleInjectionJob(job) {
        console.log("Processing injection job:", job);
        
        try {
            // 这里应该实现完整的历史注入逻辑
            // 由于篇幅限制，这里只是示例框架
            
            // 模拟处理时间
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 通知服务器任务完成
            await fetch(`${SERVER_URL}/injection_complete`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ job_id: job.job_id, status: 'success' })
            });
            
            updateConnectionStatus('success');
        } catch (error) {
            console.error("Error processing injection job:", error);
            updateConnectionStatus('error');
        }
    }

    // 初始化
    loadConfig().then(() => {
        pollForJob();
    });

    // 添加使用说明
    console.log(`
🚀 LMArena Automator (Cloud Version) 已启动

📝 使用说明：
1. 确保已修改脚本中的 SERVER_URL 为你的 Hugging Face Spaces 地址
2. 在 LMArena.ai 打开一个历史对话页面
3. 配置你的 OpenAI 客户端连接到云端 API
4. 开始使用！

⚠️ 注意：此脚本需要与云端 API 服务器配合使用
    `);

})();
