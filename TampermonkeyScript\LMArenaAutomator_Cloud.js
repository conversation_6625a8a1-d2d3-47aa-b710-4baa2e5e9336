// ==UserScript==
// @name         LMArena Automator (Cloud Version)
// @namespace    http://tampermonkey.net/
// @version      2.1
// @description  Cloud-compatible version for Hugging Face Spaces deployment
// <AUTHOR>
// @match        https://lmarena.ai/c/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // ⚠️ 重要：修改这里的服务器地址为你的Hugging Face Spaces地址
    // 例如：'https://your-username-lmarena-bridge.hf.space'
    const SERVER_URL = 'https://your-space-name.hf.space';
    
    // 如果你想使用本地服务器，请改为：
    // const SERVER_URL = 'http://127.0.0.1:5102';

    let config = {
        bypass_enabled: false,
        tavern_mode_enabled: false,
        log_server_requests: false,
        log_tampermonkey_debug: false
    };

    // --- 配置加载逻辑 (v2 - 从服务器获取) ---
    async function loadConfig() {
        try {
            const response = await fetch(`${SERVER_URL}/get_config`);
            if (response.ok) {
                config = await response.json();
                console.log("LMArena Automator: Config loaded successfully from server.", config);
            } else {
                 console.warn(`LMArena Automator: Failed to fetch config from server (status: ${response.status}). Using default values.`);
            }
        } catch (e) {
            console.error("LMArena Automator: Error fetching config from server. Using default values.", e);
        }
    }

    const STORAGE_KEY = 'pending_history_injection';
    const MODEL_ID_STORAGE_KEY = 'lmarena_target_model_id';
    let isReloading = false;

    console.log(`LMArena History Forger: Script started with server: ${SERVER_URL}`);

    // 显示连接状态
    function showConnectionStatus() {
        const statusDiv = document.createElement('div');
        statusDiv.id = 'lmarena-connection-status';
        statusDiv.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: #4CAF50;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10000;
            font-family: monospace;
        `;
        statusDiv.textContent = `🔗 Connected to: ${SERVER_URL}`;
        document.body.appendChild(statusDiv);
        
        // 3秒后淡出
        setTimeout(() => {
            statusDiv.style.opacity = '0.3';
        }, 3000);
    }

    // 页面加载完成后显示连接状态
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', showConnectionStatus);
    } else {
        showConnectionStatus();
    }

    // --- 轮询逻辑 ---
    async function pollForJob() {
        if (isReloading) return;

        console.log("LMArena History Forger: Polling for new injection job...");
        try {
            const response = await fetch(`${SERVER_URL}/get_injection_job`);
            if (response.ok) {
                const job = await response.json();
                if (job.status === 'job_available') {
                    console.log("LMArena History Forger: New injection job received:", job);
                    await handleInjectionJob(job);
                }
            }
        } catch (error) {
            console.error("LMArena History Forger: Error polling for job:", error);
        }

        if (!isReloading) {
            setTimeout(pollForJob, 2000);
        }
    }

    // 处理注入任务的函数（简化版，需要完整实现）
    async function handleInjectionJob(job) {
        console.log("Processing injection job:", job);
        // 这里需要实现完整的注入逻辑
        // 由于篇幅限制，这里只是示例框架
        
        try {
            // 通知服务器任务完成
            await fetch(`${SERVER_URL}/injection_complete`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ job_id: job.job_id, status: 'success' })
            });
        } catch (error) {
            console.error("Error notifying server:", error);
        }
    }

    // 初始化
    loadConfig().then(() => {
        pollForJob();
    });

})();
