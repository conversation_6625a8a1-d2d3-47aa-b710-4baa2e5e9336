{
  // 功能开关：绕过敏感词检测
  // 在原始用户请求的对话中，额外注入一个内容为空的用户消息，以尝试绕过敏感词审查。
  // 设置为 true 来启用该功能，设置为 false 来禁用。
  //
  // !!! 重要提示 !!!
  // 修改此文件后，您必须重启本地的 Python 服务器 (local_openai_history_server.py)
  // 才能使更改生效。
  "bypass_enabled": true,

  // 功能开关：酒馆模式 (Tavern Mode)
  // 此模式专为需要完整历史记录注入的场景设计（如酒馆AI、SillyTavern等）。
  // 1. 合并多个system提示：将多个system角色的消息合并为一个，用换行符分隔。
  // 2. 完整历史注入：每次对话都将完整的、修改后的历史记录注入到LMArena。
  // 3. 自动触发：通过模拟输入一个空格并发送来触发模型的响应。
  //
  // !!! 重要提示 !!!
  // 此模式现在可以和 "bypass_enabled" 协同工作。
  // 修改后需要重启 Python 服务器才能生效。
  "tavern_mode_enabled": true,

  // --- 日志与调试 ---

  // 开关：服务器请求体日志
  // 设置为 true，本地 Python 服务器会在控制台打印收到的完整 OpenAI 请求体。
  // 用于调试客户端发送的数据。默认为 false。
  "log_server_requests": false,

  // 开关：油猴脚本调试日志
  // 设置为 true，浏览器控制台会打印详细的 "DEBUG" 级别日志。
  // 用于追踪油猴脚本的内部工作流程。默认为 false。
  "log_tampermonkey_debug": false
}