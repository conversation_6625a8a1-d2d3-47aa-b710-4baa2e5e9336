🚀 LMArena Bridge - Hugging Face Spaces 部署包

📁 这个文件夹包含了部署到 Hugging Face Spaces 所需的所有文件
⚠️ 你的原始项目文件完全没有被修改，可以继续正常使用！

📋 部署步骤（简化版）：

1. 访问 https://huggingface.co/spaces
2. 点击 "Create new Space"
3. 选择 Gradio SDK，CPU basic 硬件
4. 将本文件夹中的所有文件上传到 Space
5. 等待自动部署完成（2-5分钟）

🔧 配置油猴脚本：

1. 安装 Tampermonkey 浏览器扩展
2. 复制 TampermonkeyScript/LMArenaAutomator_Cloud.js 的内容
3. 修改脚本中的服务器地址为你的 Space 地址
4. 在 LMArena.ai 打开历史对话页面

📱 使用 API：

你的 API 地址：https://你的用户名-space名称.hf.space/v1
在任何支持 OpenAI API 的应用中配置这个地址即可

📖 详细说明请查看：DEPLOYMENT_GUIDE.md

❓ 如有问题，请检查：
- Hugging Face Space 是否正常运行
- 油猴脚本中的服务器地址是否正确
- LMArena 页面是否在浏览器中打开
