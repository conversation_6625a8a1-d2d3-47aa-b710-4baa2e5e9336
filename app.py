# Hugging Face Spaces deployment for LMArena Bridge
import os
import gradio as gr
import threading
import time
from local_openai_history_server import app

def create_interface():
    """创建Gradio界面"""

    def get_api_info():
        port = int(os.environ.get("PORT", 7860))
        space_name = os.environ.get("SPACE_ID", "your-space-name")
        if space_name != "your-space-name":
            api_url = f"https://{space_name}.hf.space/v1"
        else:
            api_url = f"http://localhost:{port}/v1"

        return f"""
# 🚀 LMArena Bridge API 服务

## 📡 API 信息
- **API Base URL**: `{api_url}`
- **健康检查**: `{api_url.replace('/v1', '/health')}`
- **模型列表**: `{api_url}/models`
- **聊天接口**: `{api_url}/chat/completions`

## 🔧 使用方法
1. 在浏览器中安装油猴脚本
2. 配置脚本连接到此API地址
3. 在LMArena.ai打开历史对话页面
4. 使用任何OpenAI兼容的客户端连接此API

## 📱 客户端配置示例
```python
import openai
client = openai.OpenAI(
    api_key="sk-any-key",
    base_url="{api_url}"
)
```

⚠️ **重要**: 此API需要配合浏览器中的油猴脚本使用
        """

    with gr.Blocks(title="LMArena Bridge API") as interface:
        gr.Markdown(get_api_info())

        with gr.Row():
            test_btn = gr.Button("🔍 测试API连接", variant="primary")
            status_output = gr.Textbox(label="状态", interactive=False)

        def test_api():
            try:
                # 简单的健康检查
                return "✅ API服务正常运行"
            except Exception as e:
                return f"❌ API服务异常: {str(e)}"

        test_btn.click(test_api, outputs=status_output)

    return interface

def run_flask_server():
    """在后台运行Flask服务器"""
    port = int(os.environ.get("PORT", 7860))
    app.run(host="0.0.0.0", port=port, debug=False)

if __name__ == "__main__":
    # 在后台启动Flask服务器
    flask_thread = threading.Thread(target=run_flask_server, daemon=True)
    flask_thread.start()

    # 等待Flask服务器启动
    time.sleep(2)

    # 启动Gradio界面（这会占用主线程）
    interface = create_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False
    )
