# 🚀 LMArena Bridge - Hugging Face Spaces 部署指南

## 📋 概述

这是LMArena桥接器的云端部署版本，允许你通过API访问LMArena.ai上的各种大语言模型。

## 🌐 API 使用方法

### 基本信息
- **API Base URL**: `https://your-space-name.hf.space/v1`
- **API Key**: 任意值（例如：`sk-xxxxxxxx`）
- **兼容性**: 完全兼容 OpenAI API 格式

### 支持的端点
- `GET /v1/models` - 获取可用模型列表
- `POST /v1/chat/completions` - 聊天完成接口

### 示例请求

```bash
# 获取模型列表
curl -X GET "https://your-space-name.hf.space/v1/models" \
  -H "Authorization: Bearer sk-xxxxxxxx"

# 发送聊天请求
curl -X POST "https://your-space-name.hf.space/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-xxxxxxxx" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "messages": [
      {"role": "user", "content": "Hello, how are you?"}
    ],
    "stream": true
  }'
```

## 🔧 客户端配置

### OpenAI Python 库
```python
import openai

client = openai.OpenAI(
    api_key="sk-xxxxxxxx",
    base_url="https://your-space-name.hf.space/v1"
)

response = client.chat.completions.create(
    model="claude-3-5-sonnet-20241022",
    messages=[
        {"role": "user", "content": "Hello!"}
    ]
)
```

### 其他客户端
- **ChatGPT Next Web**: 设置API地址为 `https://your-space-name.hf.space/v1`
- **OpenAI Translator**: 同样设置API地址
- **任何支持OpenAI API的应用**: 都可以使用

## ⚠️ 重要说明

1. **需要浏览器配合**: 此API需要用户在浏览器中运行配套的油猴脚本
2. **首次使用**: 需要先在浏览器中打开LMArena.ai并安装油猴脚本
3. **连接配置**: 油猴脚本需要配置为连接到这个云端API而不是本地服务器

## 🐵 油猴脚本配置

修改油猴脚本中的服务器地址：
```javascript
// 将原来的本地地址
const SERVER_BASE_URL = 'http://127.0.0.1:5102';

// 改为你的Hugging Face Spaces地址
const SERVER_BASE_URL = 'https://your-space-name.hf.space';
```

## 📱 移动端使用

在手机上安装支持OpenAI API的应用，配置API地址即可使用。

## 🔗 相关链接

- [原项目GitHub](https://github.com/your-username/LMArenaBridge)
- [LMArena.ai](https://lmarena.ai/)
- [Hugging Face Spaces文档](https://huggingface.co/docs/hub/spaces)
